import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  filter,
  get,
  includes,
  isEqual,
  pick,
  map,
  isEmpty,
  flatMap,
  compact,
} from 'lodash';
import {
  Route,
  Switch,
  useHistory,
  useRouteMatch,
  useLocation,
} from 'react-router-dom';

import IEContentLibrary from '../../../../common/abstract/EContent/IEContentLibrary';
import useT from '../../../../common/components/utils/Translations/useT';
import IEContentResource from '../../../../common/abstract/EContent/IEContentResource';
import { isEContentLibraryResource } from '../../../../model/EContentLibraryResourcesTypeNames';
import AddButton from '../../../../common/components/controls/PanelButtons/AddButton';
import { EditSessionButton } from '../../../../common/components/containers/EditSessionProvider';
import ViewSelectorDropdown from './common/ViewSelectorDropdown';
import Breadcrumbs from '../../../../common/components/utils/Breadcrumbs';
import ItemsView from './views/ItemsView';
import ContentView from './views/ContentView';
import { BasicByUrl, BasicByValue } from './model/ViewModes';
import EContentItemContentFormRoute from './routes/EContentItemContentFormRoute';
import EContentItemContentBreadcrumbs from './routes/EContentItemContentBreadcrumbs';
import usePaginationRouting from '../../../../common/data/hooks/usePaginationRouting';

export interface IEContentLibraryItemsTable {
  entity: IEContentLibrary;
}

const EContentLibraryItemsTable: React.FC<IEContentLibraryItemsTable> = ({
  entity,
}) => {
  const t = useT();
  const history = useHistory();
  const { url, path } = useRouteMatch();
  const { pathname } = useLocation();
  const [itemUrl, setItemUrl] = useState(url);
  const paginationRouting = usePaginationRouting();
  useEffect(() => {
    const regexp = /libraries\/edit\/(.*\d)\/page\/(.*\d)\/size\/(.*\d)$/gm;
    if (pathname.match(regexp) || pathname.includes('content-view')) {
      setItemUrl(pathname);
    }
  }, [pathname, paginationRouting]);

  const { resourcesCount, usedKeywords } = entity;

  const [selectedResources, setSelectedResources] = useState<
    undefined | Partial<IEContentResource>[]
  >();

  const filterLanguages = useCallback(
    (options, filterValues) => {
      const filterResources = get(filterValues, ['resources'], []);
      if (isEmpty(selectedResources) && isEmpty(filterResources)) {
        //if new library and no filter then select all
        return options;
      }
      if (isEmpty(selectedResources) && !isEmpty(filterResources)) {
        setSelectedResources(filterResources);
      }

      const languageIds = compact(flatMap(filterResources, 'languageIds'));

      if (isEmpty(languageIds)) {
        return [];
      }

      return filter(options, option => includes(languageIds, option?.id));
    },
    [selectedResources],
  );

  const handleFiltersChange = useCallback(
    filter => {
      const resources = get(filter, ['resources'], null);
      if (!isEmpty(resources) && !isEqual(selectedResources, resources)) {
        setSelectedResources(resources);
      }
      return null;
    },
    [selectedResources],
  );

  const cookFilterModels = useCallback(
    models =>
      map(models, model => {
        if (isEContentLibraryResource(model)) {
          return pick(model, [
            'id',
            'name',
            '__typename',
            'folderId',
            'sequence',
            'status',
            'languageIds',
          ]);
        }
        return model;
      }),
    [],
  );

  const goToCreate = useCallback(() => history.push(`${url}/add`), [
    history,
    url,
  ]);

  const filterKey = useMemo(() => `E_CONTENT_ITEMS_TABLE_${entity?.id}`, [
    entity,
  ]);

  const section = get(useRouteMatch(`${path}/:section`), 'params.section');

  const viewMode = useMemo(() => BasicByUrl[section]?.value, [section]);

  const handleViewChange = useCallback(
    mode => {
      const section = BasicByValue[mode].url;
      const newUrl = section ? `${url}/${section}` : url;
      history.push(newUrl);
    },
    [url, history],
  );

  const rightActionButtons = useMemo(
    () => (
      <>
        <ViewSelectorDropdown value={viewMode} onChange={handleViewChange} />
        <AddButton
          isDisabled={!resourcesCount}
          title={
            resourcesCount
              ? t('Add Item')
              : t(
                  'Add at least one resource to the library before creating the item',
                )
          }
          onClick={goToCreate}
        />
        <EditSessionButton />
      </>
    ),
    [t, goToCreate, resourcesCount, viewMode, handleViewChange],
  );

  const cookQueryVariables = useCallback(
    ({ resources, ...values }) => {
      let resourceIds = map(resources, 'id');

      if (
        isEmpty(resourceIds) &&
        selectedResources &&
        selectedResources.length > 0
      ) {
        resourceIds = map(selectedResources, 'id');
      }

      return {
        ...values,
        resourceId: resourceIds,
      };
    },
    [selectedResources],
  );

  const isListItemQueryPrevented = useCallback(
    variables => !variables?.resourceId,
    [],
  );

  return (
    <Switch>
      <Route path={`${url}/edit/:itemId/contents-list/edit/:contentId`}>
        {({ match }) => (
          <EContentItemContentBreadcrumbs
            contentId={match?.params.contentId || ''}
            itemId={match?.params.itemId || ''}
            libraryId={entity?.id?.toString() || ''}
          >
            <EContentItemContentFormRoute
              libraryId={entity?.id?.toString() || ''}
              usedKeywords={usedKeywords}
            />
          </EContentItemContentBreadcrumbs>
        )}
      </Route>
      <Route path={`${url}/content-view`}>
        <Breadcrumbs.Anchor route={`${url}/content-view`} title={t('Contents')}>
          <ContentView
            cookFilterModels={cookFilterModels}
            cookQueryVariables={cookQueryVariables}
            filterKey={filterKey}
            filterLanguages={filterLanguages}
            goToCreate={goToCreate}
            isListItemQueryPrevented={isListItemQueryPrevented}
            libraryId={entity?.id}
            libraryName={entity?.name}
            parentUrl={url}
            rightActionButtons={rightActionButtons}
            selectedResources={selectedResources}
            usedKeywords={usedKeywords}
            onFilterChange={handleFiltersChange}
          />
        </Breadcrumbs.Anchor>
      </Route>
      <Route path={url}>
        <Breadcrumbs.Anchor route={itemUrl} title={entity?.name || 'Items'}>
          <ItemsView
            cookFilterModels={cookFilterModels}
            cookQueryVariables={cookQueryVariables}
            filterKey={filterKey}
            filterLanguages={filterLanguages}
            goToCreate={goToCreate}
            isListItemQueryPrevented={isListItemQueryPrevented}
            libraryId={entity?.id}
            libraryName={entity?.name}
            rightActionButtons={rightActionButtons}
            selectedResources={selectedResources}
            usedKeywords={usedKeywords}
            onFilterChange={handleFiltersChange}
          />
        </Breadcrumbs.Anchor>
      </Route>
    </Switch>
  );
};

export default EContentLibraryItemsTable;
